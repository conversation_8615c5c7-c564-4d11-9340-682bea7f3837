<template>
  <view class="home-container">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-content">
        <text class="app-title">AI换装</text>
        <view class="navbar-actions">
          <ThemeToggle class="theme-toggle-btn" />
          <view class="avatar-btn">
            <image class="avatar" src="/static/icons/avatar-default.png" mode="aspectFill" />
          </view>
        </view>
      </view>
    </view>

    <!-- 主要内容区域 -->
    <scroll-view class="main-content" scroll-y>
      <!-- 快速换装入口 -->
      <view class="quick-action-section">
        <view class="section-title">开始你的AI换装之旅</view>
        <view class="action-cards">
          <view class="action-card primary-card" @tap="goToDressup('camera')">
            <view class="card-icon">📷</view>
            <view class="card-title">拍照换装</view>
            <view class="card-desc">实时拍摄，即刻体验</view>
          </view>
          <view class="action-card secondary-card" @tap="goToDressup('album')">
            <view class="card-icon">🖼️</view>
            <view class="card-title">相册选择</view>
            <view class="card-desc">从相册选择照片</view>
          </view>
        </view>
      </view>

      <!-- 热门推荐 -->
      <view class="recommendation-section">
        <view class="section-header">
          <text class="section-title">热门推荐</text>
          <text class="more-btn" @tap="goToWardrobe">更多 ></text>
        </view>
        <scroll-view class="recommendation-list" scroll-x>
          <view class="recommendation-item" v-for="item in recommendations" :key="item.id">
            <image class="item-image" :src="item.image" mode="aspectFill" />
            <view class="item-info">
              <text class="item-name">{{ item.name }}</text>
              <text class="item-price">¥{{ item.price }}</text>
            </view>
          </view>
        </scroll-view>
      </view>

      <!-- 分类导航 -->
      <view class="category-section">
        <view class="section-title">服装分类</view>
        <view class="category-grid">
          <view class="category-item" v-for="category in categories" :key="category.id" @tap="goToCategory(category.id)">
            <view class="category-icon">{{ category.icon }}</view>
            <text class="category-name">{{ category.name }}</text>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import ThemeToggle from '@/components/ThemeToggle.vue'

export default {
  components: {
    ThemeToggle
  },
  data() {
    return {
      recommendations: [
        { id: 1, name: '时尚连衣裙', price: 299, image: 'https://images.unsplash.com/photo-1595777457583-95e059d581b8?w=300' },
        { id: 2, name: '休闲T恤', price: 89, image: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=300' },
        { id: 3, name: '商务西装', price: 899, image: 'https://images.unsplash.com/photo-1594938298603-c8148c4dae35?w=300' },
        { id: 4, name: '牛仔裤', price: 199, image: 'https://images.unsplash.com/photo-1542272604-787c3835535d?w=300' }
      ],
      categories: [
        { id: 1, name: '上衣', icon: '👕' },
        { id: 2, name: '裤装', icon: '👖' },
        { id: 3, name: '裙装', icon: '👗' },
        { id: 4, name: '外套', icon: '🧥' },
        { id: 5, name: '配饰', icon: '👜' },
        { id: 6, name: '鞋履', icon: '👠' }
      ]
    }
  },
  onLoad() {
    // 获取系统状态栏高度
    const systemInfo = uni.getSystemInfoSync()
    this.statusBarHeight = systemInfo.statusBarHeight
  },
  methods: {
    goToDressup(type) {
      uni.navigateTo({
        url: `/pages/dressup/dressup?type=${type}`
      })
    },
    goToWardrobe() {
      uni.switchTab({
        url: '/pages/wardrobe/wardrobe'
      })
    },
    goToCategory(categoryId) {
      uni.navigateTo({
        url: `/pages/wardrobe/wardrobe?category=${categoryId}`
      })
    }
  }
}
</script>

<style lang="scss" scoped>
// 导入依赖
@use '../../styles/variables' as *;
@use '../../styles/mixins' as *;

.home-container {
  min-height: 100vh;
  @include gradient-bg(135deg, #f5f7fa, #c3cfe2);
  @include contain(layout style);
}

// 暗色模式背景
[data-theme="dark"] .home-container {
  background: linear-gradient(135deg, #0e0e10 0%, #1c1c1f 100%);
}

// 纯黑模式背景（OLED友好）
[data-theme="black"] .home-container {
  background: linear-gradient(135deg, #000000 0%, #0a0a0a 100%);
}

.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: var(--z-fixed);
  background: var(--bg-overlay);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--divider-color);
  height: 60px;
  @include transition(all);
}

.navbar-content {
  @include flex-between;
  @include safe-area(top);
  padding: $navbar-padding $spacing-5 $spacing-3;
}

.app-title {
  font-size: $font-size-2xl;
  font-weight: $font-weight-bold;
  color: var(--primary-color);
  @include text-gradient(linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%));
}

.navbar-actions {
  @include flex-center;
  gap: $spacing-3;
}

.avatar-btn {
  .avatar {
    width: 32px;
    height: 32px;
    border-radius: $radius-full;
    border: 2px solid var(--primary-color);
    @include transition(all);

    &:hover {
      border-color: var(--primary-hover);
      transform: scale(1.02);
    }
  }
}

.main-content {
  padding-top: 100px;
  padding-bottom: 20px;
}

.quick-action-section {
  padding: 30px 20px;
  
  .section-title {
    font-size: 28px;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 25px;
    text-align: center;
    background: linear-gradient(135deg, #0052D9 0%, #FF8A00 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
    letter-spacing: 1px;
    position: relative;
    
    &::after {
      content: '';
      position: absolute;
      bottom: -8px;
      left: 50%;
      transform: translateX(-50%);
      width: 60px;
      height: 3px;
      background: linear-gradient(135deg, #0052D9 0%, #FF8A00 100%);
      border-radius: 3px;
    }
  }
  
  .action-cards {
    display: flex;
    gap: 15px;
    
    .action-card {
      flex: 1;
      padding: 30px 20px;
      border-radius: 20px;
      text-align: center;
      position: relative;
      overflow: hidden;
      
      .card-icon {
        font-size: 40px;
        margin-bottom: 10px;
      }
      
      .card-title {
        font-size: 18px;
        font-weight: 600;
        color: white;
        margin-bottom: 5px;
      }
      
      .card-desc {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.8);
      }
      
      &.primary-card {
        background: linear-gradient(135deg, #0052D9 0%, #366ef4 100%);
        box-shadow: 0 8px 25px rgba(0, 82, 217, 0.3);
      }
      
      &.secondary-card {
        background: linear-gradient(135deg, #FF8A00 0%, #FFB366 100%);
        box-shadow: 0 8px 25px rgba(255, 138, 0, 0.3);
      }
    }
  }
}

.recommendation-section {
  padding: 0 20px 30px;
  
  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    
    .section-title {
      font-size: 18px;
      font-weight: 600;
      color: var(--text-primary);
    }
    
    .more-btn {
      font-size: 14px;
      color: #0052D9;
    }
  }
  
  .recommendation-list {
    white-space: nowrap;
    
    .recommendation-item {
      display: inline-block;
      width: 120px;
      margin-right: 15px;
      background: var(--bg-primary);
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      
      .item-image {
        width: 100%;
        height: 120px;
      }
      
      .item-info {
        padding: 10px;
        
        .item-name {
          font-size: 14px;
          color: var(--text-primary);
          display: block;
          margin-bottom: 5px;
        }
        
        .item-price {
          font-size: 16px;
          font-weight: 600;
          color: #FF8A00;
        }
      }
    }
  }
}

.category-section {
  padding: 0 20px;
  
  .section-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 15px;
  }
  
  .category-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
    
    .category-item {
      background: var(--bg-primary);
      padding: 20px;
      border-radius: 16px;
      text-align: center;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
      transition: transform 0.2s;
      
      &:active {
        transform: scale(0.95);
      }
      
      .category-icon {
        font-size: 32px;
        margin-bottom: 8px;
      }
      
      .category-name {
        font-size: 14px;
        color: var(--text-secondary);
      }
    }
  }
}
</style>