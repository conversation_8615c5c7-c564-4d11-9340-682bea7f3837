// 全局样式文件 - 优化版本
// 导入样式模块（按依赖顺序）
@use 'sass:color';
@use 'sass:math';
@use 'variables' as *;
@use 'mixins' as *;
@use 'utilities';
@use 'responsive';

// ==================== 重置样式 ====================

// 现代CSS重置
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  line-height: 1.15;
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: transparent;
}

body {
  margin: 0;
  font-family: $font-family-base;
  font-size: $font-size-base;
  line-height: $line-height-normal;
  color: var(--text-primary);
  background-color: var(--bg-secondary);
  @include font-smooth;
  @include transition(background-color, color);
}

// ==================== CSS变量系统 ====================

:root {
  // 主题色彩
  --primary-color: #{$primary-color};
  --primary-light: #{$primary-light};
  --primary-dark: #{$primary-dark};
  --primary-hover: #{$primary-hover};
  --primary-active: #{$primary-active};
  --primary-disabled: #{$primary-disabled};

  --secondary-color: #{$secondary-color};
  --secondary-light: #{$secondary-light};
  --secondary-dark: #{$secondary-dark};
  --secondary-hover: #{$secondary-hover};
  --secondary-active: #{$secondary-active};
  --secondary-disabled: #{$secondary-disabled};

  // 功能色彩
  --success-color: #{$success-color};
  --success-light: #{$success-light};
  --success-dark: #{$success-dark};
  --warning-color: #{$warning-color};
  --warning-light: #{$warning-light};
  --warning-dark: #{$warning-dark};
  --error-color: #{$error-color};
  --error-light: #{$error-light};
  --error-dark: #{$error-dark};
  --info-color: #{$info-color};
  --info-light: #{$info-light};
  --info-dark: #{$info-dark};

  // 中性色彩
  --text-primary: #{$text-primary};
  --text-secondary: #{$text-secondary};
  --text-tertiary: #{$text-tertiary};
  --text-quaternary: #{$text-quaternary};
  --text-placeholder: #{$text-placeholder};
  --text-disabled: #{$text-disabled};
  --text-inverse: #{$text-inverse};

  --bg-primary: #{$bg-primary};
  --bg-secondary: #{$bg-secondary};
  --bg-tertiary: #{$bg-tertiary};
  --bg-quaternary: #{$bg-quaternary};
  --bg-disabled: #{$bg-disabled};
  --bg-mask: #{$bg-mask};
  --bg-overlay: #{$bg-overlay};

  --border-color: #{$border-color};
  --border-color-light: #{$border-color-light};
  --border-color-dark: #{$border-color-dark};
  --divider-color: #{$divider-color};

  // 阴影系统
  --shadow-1: #{$shadow-1};
  --shadow-2: #{$shadow-2};
  --shadow-3: #{$shadow-3};
  --shadow-4: #{$shadow-4};
  --shadow-5: #{$shadow-5};
  --shadow-6: #{$shadow-6};

  // 圆角系统
  --radius-xs: #{$radius-xs};
  --radius-sm: #{$radius-sm};
  --radius-md: #{$radius-md};
  --radius-lg: #{$radius-lg};
  --radius-xl: #{$radius-xl};
  --radius-2xl: #{$radius-2xl};
  --radius-3xl: #{$radius-3xl};
  --radius-full: #{$radius-full};

  // 间距系统
  --spacing-0: #{$spacing-0};
  --spacing-1: #{$spacing-1};
  --spacing-2: #{$spacing-2};
  --spacing-3: #{$spacing-3};
  --spacing-4: #{$spacing-4};
  --spacing-5: #{$spacing-5};
  --spacing-6: #{$spacing-6};
  --spacing-8: #{$spacing-8};
  --spacing-10: #{$spacing-10};
  --spacing-12: #{$spacing-12};
  --spacing-16: #{$spacing-16};
  --spacing-20: #{$spacing-20};
  --spacing-24: #{$spacing-24};
  --spacing-32: #{$spacing-32};

  // 字体系统
  --font-size-xs: #{$font-size-xs};
  --font-size-sm: #{$font-size-sm};
  --font-size-base: #{$font-size-base};
  --font-size-lg: #{$font-size-lg};
  --font-size-xl: #{$font-size-xl};
  --font-size-2xl: #{$font-size-2xl};
  --font-size-3xl: #{$font-size-3xl};
  --font-size-4xl: #{$font-size-4xl};
  --font-size-5xl: #{$font-size-5xl};

  --line-height-tight: #{$line-height-tight};
  --line-height-snug: #{$line-height-snug};
  --line-height-normal: #{$line-height-normal};
  --line-height-relaxed: #{$line-height-relaxed};
  --line-height-loose: #{$line-height-loose};

  --font-weight-thin: #{$font-weight-thin};
  --font-weight-light: #{$font-weight-light};
  --font-weight-normal: #{$font-weight-normal};
  --font-weight-medium: #{$font-weight-medium};
  --font-weight-semibold: #{$font-weight-semibold};
  --font-weight-bold: #{$font-weight-bold};
  --font-weight-extrabold: #{$font-weight-extrabold};
  --font-weight-black: #{$font-weight-black};

  // 过渡动画
  --transition-fast: #{$transition-fast} ease;
  --transition-base: #{$transition-base} ease;
  --transition-slow: #{$transition-slow} ease;
  --transition-slower: #{$transition-slower} ease;

  // Z-index 层级
  --z-dropdown: #{$z-dropdown};
  --z-sticky: #{$z-sticky};
  --z-fixed: #{$z-fixed};
  --z-modal-backdrop: #{$z-modal-backdrop};
  --z-modal: #{$z-modal};
  --z-popover: #{$z-popover};
  --z-tooltip: #{$z-tooltip};
  --z-toast: #{$z-toast};
}

// ==================== 暗色主题 ====================

[data-theme="dark"] {
  // 主题色彩 - 暗色模式优化
  --primary-color: #{color.adjust($primary-color, $lightness: 10%)};
  --primary-light: #{color.adjust($primary-light, $lightness: 5%)};
  --primary-dark: #{$primary-color};
  --primary-hover: #{color.adjust($primary-hover, $lightness: 5%)};
  --primary-active: #{color.adjust($primary-active, $lightness: -5%)};
  --primary-disabled: #{color.mix($primary-color, $dark-bg-primary, 30%)};

  --secondary-color: #{color.adjust($secondary-color, $lightness: 10%)};
  --secondary-light: #{color.adjust($secondary-light, $lightness: 5%)};
  --secondary-dark: #{$secondary-color};
  --secondary-hover: #{color.adjust($secondary-hover, $lightness: 5%)};
  --secondary-active: #{color.adjust($secondary-active, $lightness: -5%)};
  --secondary-disabled: #{color.mix($secondary-color, $dark-bg-primary, 30%)};

  // 功能色彩
  --success-color: #{$success-light};
  --success-light: #{color.adjust($success-light, $lightness: 5%)};
  --success-dark: #{$success-color};
  --warning-color: #{$warning-light};
  --warning-light: #{color.adjust($warning-light, $lightness: 5%)};
  --warning-dark: #{$warning-color};
  --error-color: #{$error-light};
  --error-light: #{color.adjust($error-light, $lightness: 5%)};
  --error-dark: #{$error-color};
  --info-color: #{$info-light};
  --info-light: #{color.adjust($info-light, $lightness: 5%)};
  --info-dark: #{$info-color};

  // 中性色彩 - 暗色主题
  --text-primary: #{$dark-text-primary};
  --text-secondary: #{$dark-text-secondary};
  --text-tertiary: #{$dark-text-tertiary};
  --text-quaternary: #{$dark-text-quaternary};
  --text-placeholder: #{$dark-text-placeholder};
  --text-disabled: #{$dark-text-disabled};
  --text-inverse: #{$dark-text-inverse};

  --bg-primary: #{$dark-bg-primary};
  --bg-secondary: #{$dark-bg-secondary};
  --bg-tertiary: #{$dark-bg-tertiary};
  --bg-quaternary: #{$dark-bg-quaternary};
  --bg-disabled: #{$dark-bg-disabled};
  --bg-mask: #{$dark-bg-mask};
  --bg-overlay: #{$dark-bg-overlay};

  --border-color: #{$dark-border-color};
  --border-color-light: #{$dark-border-color-light};
  --border-color-dark: #{$dark-border-color-dark};
  --divider-color: #{$dark-divider-color};

  // 暗色主题阴影
  --shadow-1: #{$dark-shadow-1};
  --shadow-2: #{$dark-shadow-2};
  --shadow-3: #{$dark-shadow-3};
  --shadow-4: #{$dark-shadow-4};
  --shadow-5: #{$dark-shadow-5};
  --shadow-6: #{$dark-shadow-6};
}

// ==================== 纯黑主题（OLED友好） ====================
[data-theme="black"] {
  // 主题色彩 - 使用暗色基础并做适配
  --primary-color: #{color.adjust($primary-color, $lightness: 12%)};
  --primary-light: #{color.adjust($primary-light, $lightness: 8%)};
  --primary-dark: #{$primary-color};
  --primary-hover: #{color.adjust($primary-hover, $lightness: 8%)};
  --primary-active: #{color.adjust($primary-active, $lightness: -6%)};
  --primary-disabled: #{color.mix($primary-color, #000000, 20%)};

  --secondary-color: #{color.adjust($secondary-color, $lightness: 12%)};
  --secondary-light: #{color.adjust($secondary-light, $lightness: 8%)};
  --secondary-dark: #{$secondary-color};
  --secondary-hover: #{color.adjust($secondary-hover, $lightness: 8%)};
  --secondary-active: #{color.adjust($secondary-active, $lightness: -6%)};
  --secondary-disabled: #{color.mix($secondary-color, #000000, 20%)};

  // 功能色彩
  --success-color: #{$success-light};
  --success-light: #{color.adjust($success-light, $lightness: 6%)};
  --success-dark: #{$success-color};
  --warning-color: #{$warning-light};
  --warning-light: #{color.adjust($warning-light, $lightness: 6%)};
  --warning-dark: #{$warning-color};
  --error-color: #{$error-light};
  --error-light: #{color.adjust($error-light, $lightness: 6%)};
  --error-dark: #{$error-color};
  --info-color: #{$info-light};
  --info-light: #{color.adjust($info-light, $lightness: 6%)};
  --info-dark: #{$info-color};

  // 中性色彩 - 纯黑主题
  --text-primary: #{$dark-text-primary};
  --text-secondary: #{$dark-text-secondary};
  --text-tertiary: #{$dark-text-tertiary};
  --text-quaternary: #{$dark-text-quaternary};
  --text-placeholder: #{$dark-text-placeholder};
  --text-disabled: #{$dark-text-disabled};
  --text-inverse: #{$dark-text-inverse};

  --bg-primary: #000000;
  --bg-secondary: #0a0a0a;
  --bg-tertiary: #111111;
  --bg-quaternary: #1a1a1a;
  --bg-disabled: #111111;
  --bg-mask: rgba(0, 0, 0, 0.85);
  --bg-overlay: rgba(0, 0, 0, 0.92);

  --border-color: #222222;
  --border-color-light: #1a1a1a;
  --border-color-dark: #2a2a2a;
  --divider-color: #1a1a1a;

  // 阴影
  --shadow-1: #{$dark-shadow-1};
  --shadow-2: #{$dark-shadow-2};
  --shadow-3: #{$dark-shadow-3};
  --shadow-4: #{$dark-shadow-4};
  --shadow-5: #{$dark-shadow-5};
  --shadow-6: #{$dark-shadow-6};
}

// ==================== H5 TabBar 主题覆盖 ====================
// 说明：H5 平台原生 TabBar 样式无法通过 API 设置，这里用选择器覆盖
[data-theme="dark"] .uni-tabbar, 
[data-theme="dark"] .uni-tabbar__wrapper {
  background-color: #1a1a1a !important;
  border-color: #000000 !important;
}

[data-theme="dark"] .uni-tabbar__bd, 
[data-theme="dark"] .uni-tabbar__item, 
[data-theme="dark"] .uni-tabbar__label {
  color: #bfbfbf !important;
}

[data-theme="black"] .uni-tabbar, 
[data-theme="black"] .uni-tabbar__wrapper {
  background-color: #000000 !important;
  border-color: #000000 !important;
}

[data-theme="black"] .uni-tabbar__bd, 
[data-theme="black"] .uni-tabbar__item, 
[data-theme="black"] .uni-tabbar__label {
  color: #c6c6c6 !important;
}

// ==================== 系统主题检测 ====================

@media (prefers-color-scheme: dark) {
  :root:not([data-theme]) {
    // 自动应用暗色主题变量
    --primary-color: #4096ff;
    --primary-light: #69b1ff;
    --primary-dark: #1677ff;
    --primary-hover: #5aa1ff;
    --primary-active: #0958d9;
    --primary-disabled: #1c3a5c;

    --secondary-color: #ffa940;
    --secondary-light: #ffbb73;
    --secondary-dark: #ff9c1a;
    --secondary-hover: #ffb366;
    --secondary-active: #d48806;
    --secondary-disabled: #5c3d1a;

    // 功能色彩
    --success-color: #73d13d;
    --success-light: #95de64;
    --success-dark: #52c41a;
    --warning-color: #ffc53d;
    --warning-light: #ffd666;
    --warning-dark: #faad14;
    --error-color: #ff7875;
    --error-light: #ff9c99;
    --error-dark: #ff4d4f;
    --info-color: #40a9ff;
    --info-light: #69c0ff;
    --info-dark: #1890ff;

    // 中性色彩 - 暗色主题
    --text-primary: #{$dark-text-primary};
    --text-secondary: #{$dark-text-secondary};
    --text-tertiary: #{$dark-text-tertiary};
    --text-quaternary: #{$dark-text-quaternary};
    --text-placeholder: #{$dark-text-placeholder};
    --text-disabled: #{$dark-text-disabled};
    --text-inverse: #{$dark-text-inverse};

    --bg-primary: #{$dark-bg-primary};
    --bg-secondary: #{$dark-bg-secondary};
    --bg-tertiary: #{$dark-bg-tertiary};
    --bg-quaternary: #{$dark-bg-quaternary};
    --bg-disabled: #{$dark-bg-disabled};
    --bg-mask: #{$dark-bg-mask};
    --bg-overlay: #{$dark-bg-overlay};

    --border-color: #{$dark-border-color};
    --border-color-light: #{$dark-border-color-light};
    --border-color-dark: #{$dark-border-color-dark};
    --divider-color: #{$dark-divider-color};

    // 暗色主题阴影
    --shadow-1: #{$dark-shadow-1};
    --shadow-2: #{$dark-shadow-2};
    --shadow-3: #{$dark-shadow-3};
    --shadow-4: #{$dark-shadow-4};
    --shadow-5: #{$dark-shadow-5};
    --shadow-6: #{$dark-shadow-6};
  }
}

// 通用工具类
.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

// 渐变背景工具类
.gradient-primary {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
}

.gradient-secondary {
  background: linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-light) 100%);
}

.gradient-cool {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-warm {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

// 卡片样式系统
.card {
  background: var(--bg-primary);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-2);
  border: 1px solid var(--border-color-light);
  overflow: hidden;
  transition: var(--transition-base);

  &:hover {
    box-shadow: var(--shadow-3);
    transform: translateY(-1px);
  }
}

.card-flat {
  box-shadow: none;
  border: 1px solid var(--border-color);
}

.card-elevated {
  box-shadow: var(--shadow-4);

  &:hover {
    box-shadow: var(--shadow-5);
    transform: translateY(-2px);
  }
}

.card-interactive {
  cursor: pointer;
  transition: all var(--transition-base);

  &:hover {
    box-shadow: var(--shadow-4);
    transform: translateY(-2px);
  }

  &:active {
    transform: translateY(0);
    box-shadow: var(--shadow-2);
  }
}

.card-header {
  padding: var(--spacing-5) var(--spacing-5) var(--spacing-3);
  border-bottom: 1px solid var(--divider-color);
}

.card-body {
  padding: var(--spacing-5);
}

.card-footer {
  padding: var(--spacing-3) var(--spacing-5) var(--spacing-5);
  border-top: 1px solid var(--divider-color);
  background: var(--bg-secondary);
}

// 按钮样式系统
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-5);
  border: 1px solid transparent;
  border-radius: var(--radius-2xl);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-tight);
  text-decoration: none;
  cursor: pointer;
  user-select: none;
  transition: all var(--transition-base);
  position: relative;
  overflow: hidden;

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
  }

  &:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
  }

  &:active {
    transform: scale(0.98);
  }

  // 按钮尺寸
  &.btn-xs {
    padding: var(--spacing-1) var(--spacing-3);
    font-size: var(--font-size-xs);
    border-radius: var(--radius-lg);
  }

  &.btn-sm {
    padding: var(--spacing-2) var(--spacing-4);
    font-size: var(--font-size-sm);
    border-radius: var(--radius-xl);
  }

  &.btn-lg {
    padding: var(--spacing-4) var(--spacing-6);
    font-size: var(--font-size-lg);
    border-radius: var(--radius-3xl);
  }

  &.btn-xl {
    padding: var(--spacing-5) var(--spacing-8);
    font-size: var(--font-size-xl);
    border-radius: var(--radius-3xl);
  }

  // 按钮形状
  &.btn-square {
    border-radius: var(--radius-lg);
  }

  &.btn-round {
    border-radius: var(--radius-full);
  }

  &.btn-block {
    width: 100%;
  }
}

// 主要按钮
.btn-primary {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: var(--text-inverse);

  &:hover {
    background: var(--primary-hover);
    border-color: var(--primary-hover);
    box-shadow: var(--shadow-3);
  }

  &:active {
    background: var(--primary-active);
    border-color: var(--primary-active);
  }
}

// 次要按钮
.btn-secondary {
  background: var(--secondary-color);
  border-color: var(--secondary-color);
  color: var(--text-inverse);

  &:hover {
    background: var(--secondary-hover);
    border-color: var(--secondary-hover);
    box-shadow: var(--shadow-3);
  }

  &:active {
    background: var(--secondary-active);
    border-color: var(--secondary-active);
  }
}

// 成功按钮
.btn-success {
  background: var(--success-color);
  border-color: var(--success-color);
  color: var(--text-inverse);

  &:hover {
    background: var(--success-light);
    border-color: var(--success-light);
    box-shadow: var(--shadow-3);
  }
}

// 警告按钮
.btn-warning {
  background: var(--warning-color);
  border-color: var(--warning-color);
  color: var(--text-inverse);

  &:hover {
    background: var(--warning-light);
    border-color: var(--warning-light);
    box-shadow: var(--shadow-3);
  }
}

// 危险按钮
.btn-danger {
  background: var(--error-color);
  border-color: var(--error-color);
  color: var(--text-inverse);

  &:hover {
    background: var(--error-light);
    border-color: var(--error-light);
    box-shadow: var(--shadow-3);
  }
}

// 轮廓按钮
.btn-outline {
  background: transparent;
  border-color: var(--primary-color);
  color: var(--primary-color);

  &:hover {
    background: var(--primary-color);
    color: var(--text-inverse);
    box-shadow: var(--shadow-3);
  }
}

.btn-outline-secondary {
  background: transparent;
  border-color: var(--secondary-color);
  color: var(--secondary-color);

  &:hover {
    background: var(--secondary-color);
    color: var(--text-inverse);
  }
}

// 幽灵按钮
.btn-ghost {
  background: transparent;
  border-color: var(--border-color);
  color: var(--text-primary);

  &:hover {
    background: var(--bg-secondary);
    border-color: var(--border-color-dark);
  }
}

// 文本按钮
.btn-text {
  background: transparent;
  border-color: transparent;
  color: var(--primary-color);
  padding: var(--spacing-2) var(--spacing-3);

  &:hover {
    background: var(--bg-secondary);
    color: var(--primary-hover);
  }
}

// 链接按钮
.btn-link {
  background: transparent;
  border-color: transparent;
  color: var(--primary-color);
  text-decoration: underline;
  padding: 0;

  &:hover {
    color: var(--primary-hover);
  }
}

// 输入框样式系统
.input {
  width: 100%;
  padding: var(--spacing-3) var(--spacing-4);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-xl);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  background: var(--bg-primary);
  color: var(--text-primary);
  transition: all var(--transition-base);

  &:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(var(--primary-color), 0.1);
  }

  &:hover:not(:focus) {
    border-color: var(--border-color-dark);
  }

  &::placeholder {
    color: var(--text-placeholder);
  }

  &:disabled {
    background: var(--bg-disabled);
    color: var(--text-disabled);
    cursor: not-allowed;
    opacity: 0.6;
  }

  &.input-error {
    border-color: var(--error-color);

    &:focus {
      box-shadow: 0 0 0 3px rgba(var(--error-color), 0.1);
    }
  }

  &.input-success {
    border-color: var(--success-color);

    &:focus {
      box-shadow: 0 0 0 3px rgba(var(--success-color), 0.1);
    }
  }

  // 输入框尺寸
  &.input-sm {
    padding: var(--spacing-2) var(--spacing-3);
    font-size: var(--font-size-sm);
    border-radius: var(--radius-lg);
  }

  &.input-lg {
    padding: var(--spacing-4) var(--spacing-5);
    font-size: var(--font-size-lg);
    border-radius: var(--radius-2xl);
  }
}

// 输入框组
.input-group {
  display: flex;
  align-items: stretch;

  .input {
    border-radius: 0;

    &:first-child {
      border-top-left-radius: var(--radius-xl);
      border-bottom-left-radius: var(--radius-xl);
    }

    &:last-child {
      border-top-right-radius: var(--radius-xl);
      border-bottom-right-radius: var(--radius-xl);
    }

    &:not(:last-child) {
      border-right: none;
    }
  }

  .input-group-text {
    display: flex;
    align-items: center;
    padding: var(--spacing-3) var(--spacing-4);
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    font-size: var(--font-size-base);

    &:first-child {
      border-top-left-radius: var(--radius-xl);
      border-bottom-left-radius: var(--radius-xl);
      border-right: none;
    }

    &:last-child {
      border-top-right-radius: var(--radius-xl);
      border-bottom-right-radius: var(--radius-xl);
      border-left: none;
    }
  }
}

// 文本域
.textarea {
  @extend .input;
  min-height: 80px;
  resize: vertical;
  line-height: var(--line-height-relaxed);
}

// 选择框
.select {
  @extend .input;
  cursor: pointer;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right var(--spacing-3) center;
  background-repeat: no-repeat;
  background-size: 16px 12px;
  padding-right: var(--spacing-10);

  &:focus {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%230052D9' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  }
}

// 复选框和单选框
.checkbox,
.radio {
  appearance: none;
  width: 16px;
  height: 16px;
  border: 2px solid var(--border-color);
  background: var(--bg-primary);
  cursor: pointer;
  transition: all var(--transition-base);

  &:checked {
    background: var(--primary-color);
    border-color: var(--primary-color);
  }

  &:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
  }
}

.checkbox {
  border-radius: var(--radius-sm);

  &:checked {
    background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='m13.854 3.646-7.5 7.5a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L6 10.293l7.146-7.147a.5.5 0 0 1 .708.708z'/%3e%3c/svg%3e");
  }
}

.radio {
  border-radius: var(--radius-full);

  &:checked {
    background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3ccircle cx='8' cy='8' r='3'/%3e%3c/svg%3e");
  }
}

// 动画效果系统
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(var(--spacing-4));
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(var(--spacing-6));
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(calc(var(--spacing-6) * -1));
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(calc(var(--spacing-6) * -1));
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(var(--spacing-6));
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    transform: translateY(-100%);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes slideLeft {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes slideRight {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes scaleOut {
  from {
    opacity: 1;
    transform: scale(1);
  }
  to {
    opacity: 0;
    transform: scale(0.8);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes heartbeat {
  0%, 100% {
    transform: scale(1);
  }
  14% {
    transform: scale(1.1);
  }
  28% {
    transform: scale(1);
  }
  42% {
    transform: scale(1.1);
  }
  70% {
    transform: scale(1);
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
    transform: translate3d(0, -30px, 0);
  }
  70% {
    animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0, -4px, 0);
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-10px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(10px);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes loading {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

// 动画工具类
.animate-fade-in {
  animation: fadeIn var(--transition-base) ease-out;
}

.animate-fade-in-up {
  animation: fadeInUp var(--transition-base) ease-out;
}

.animate-fade-in-down {
  animation: fadeInDown var(--transition-base) ease-out;
}

.animate-fade-in-left {
  animation: fadeInLeft var(--transition-base) ease-out;
}

.animate-fade-in-right {
  animation: fadeInRight var(--transition-base) ease-out;
}

.animate-slide-up {
  animation: slideUp var(--transition-base) ease-out;
}

.animate-slide-down {
  animation: slideDown var(--transition-base) ease-out;
}

.animate-slide-left {
  animation: slideLeft var(--transition-base) ease-out;
}

.animate-slide-right {
  animation: slideRight var(--transition-base) ease-out;
}

.animate-scale-in {
  animation: scaleIn var(--transition-base) ease-out;
}

.animate-scale-out {
  animation: scaleOut var(--transition-base) ease-out;
}

.animate-pulse {
  animation: pulse 2s infinite;
}

.animate-heartbeat {
  animation: heartbeat 1.5s infinite;
}

.animate-bounce {
  animation: bounce 1s infinite;
}

.animate-shake {
  animation: shake 0.5s ease-in-out;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.animate-loading {
  animation: loading 1s linear infinite;
}

// 兼容旧版本
.fade-in {
  animation: fadeIn var(--transition-base) ease-out;
}

.slide-up {
  animation: slideUp var(--transition-base) ease-out;
}

.pulse {
  animation: pulse 2s infinite;
}

.spin {
  animation: spin 1s linear infinite;
}

// 过渡效果
.transition-all {
  transition: all var(--transition-base);
}

.transition-colors {
  transition: color var(--transition-base), background-color var(--transition-base), border-color var(--transition-base);
}

.transition-opacity {
  transition: opacity var(--transition-base);
}

.transition-transform {
  transition: transform var(--transition-base);
}

.transition-shadow {
  transition: box-shadow var(--transition-base);
}

// 微交互效果
.hover-lift {
  transition: transform var(--transition-base), box-shadow var(--transition-base);

  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-4);
  }
}

.hover-scale {
  transition: transform var(--transition-base);

  &:hover {
    transform: scale(1.02);
  }
}

.hover-glow {
  transition: box-shadow var(--transition-base);

  &:hover {
    box-shadow: 0 0 20px rgba(var(--primary-color), 0.3);
  }
}

.active-scale {
  transition: transform var(--transition-fast);

  &:active {
    transform: scale(0.98);
  }
}

// 响应式断点系统
:root {
  // 断点定义
  --breakpoint-xs: 375px;
  --breakpoint-sm: 576px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 992px;
  --breakpoint-xl: 1200px;
  --breakpoint-2xl: 1400px;
}

// 超小屏幕 (手机竖屏)
@media (max-width: 374px) {
  :root {
    --spacing-container: var(--spacing-4);
    --font-size-base: 13px;
    --radius-base: var(--radius-sm);
  }

  .hide-xs {
    display: none !important;
  }

  .container {
    padding-left: var(--spacing-3);
    padding-right: var(--spacing-3);
  }

  .btn {
    padding: var(--spacing-2) var(--spacing-4);
    font-size: var(--font-size-sm);
  }
}

// 小屏幕 (手机)
@media (min-width: 375px) and (max-width: 575px) {
  :root {
    --spacing-container: var(--spacing-4);
  }

  .hide-sm {
    display: none !important;
  }

  .show-sm {
    display: block !important;
  }
}

// 中等屏幕 (平板竖屏)
@media (min-width: 576px) and (max-width: 767px) {
  :root {
    --spacing-container: var(--spacing-5);
  }

  .hide-md {
    display: none !important;
  }

  .show-md {
    display: block !important;
  }

  .container {
    max-width: 540px;
    margin: 0 auto;
  }
}

// 大屏幕 (平板横屏/小桌面)
@media (min-width: 768px) and (max-width: 991px) {
  :root {
    --spacing-container: var(--spacing-6);
  }

  .hide-lg {
    display: none !important;
  }

  .show-lg {
    display: block !important;
  }

  .container {
    max-width: 720px;
    margin: 0 auto;
  }
}

// 超大屏幕 (桌面)
@media (min-width: 992px) and (max-width: 1199px) {
  :root {
    --spacing-container: var(--spacing-8);
  }

  .hide-xl {
    display: none !important;
  }

  .show-xl {
    display: block !important;
  }

  .container {
    max-width: 960px;
    margin: 0 auto;
  }
}

// 超超大屏幕 (大桌面)
@media (min-width: 1200px) {
  :root {
    --spacing-container: var(--spacing-10);
  }

  .hide-2xl {
    display: none !important;
  }

  .show-2xl {
    display: block !important;
  }

  .container {
    max-width: 1140px;
    margin: 0 auto;
  }
}

// 兼容旧版本
@media (max-width: 768px) {
  .hide-mobile {
    display: none !important;
  }

  .show-mobile {
    display: block !important;
  }
}

@media (min-width: 769px) {
  .hide-desktop {
    display: none !important;
  }

  .show-desktop {
    display: block !important;
  }
}

// 响应式网格系统
.row {
  display: flex;
  flex-wrap: wrap;
  margin-left: calc(var(--spacing-3) * -1);
  margin-right: calc(var(--spacing-3) * -1);
}

.col {
  flex: 1;
  padding-left: var(--spacing-3);
  padding-right: var(--spacing-3);
}

// 响应式网格系统已移至 responsive.scss

// 安全区域适配
.safe-area-top {
  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
}

.safe-area-bottom {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

// 无障碍支持
// 屏幕阅读器专用内容
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.sr-only-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  padding: inherit;
  margin: inherit;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

// 高对比度模式支持
@media (prefers-contrast: high) {
  :root {
    --border-color: #000000;
    --text-primary: #000000;
    --bg-primary: #ffffff;
  }

  [data-theme="dark"] {
    --border-color: #ffffff;
    --text-primary: #ffffff;
    --bg-primary: #000000;
  }

  .btn {
    border-width: 2px;
  }

  .input {
    border-width: 2px;
  }
}

// 减少动画偏好
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

// 焦点可见性
.focus-visible {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

// 跳转链接
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--primary-color);
  color: var(--text-inverse);
  padding: var(--spacing-2) var(--spacing-4);
  border-radius: var(--radius-lg);
  text-decoration: none;
  z-index: var(--z-toast);
  transition: top var(--transition-base);

  &:focus {
    top: 6px;
  }
}

// 错误和成功状态的图标支持
.status-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  border-radius: var(--radius-full);
  font-size: 12px;

  &.status-success {
    background: var(--success-color);
    color: var(--text-inverse);

    &::before {
      content: "✓";
    }
  }

  &.status-error {
    background: var(--error-color);
    color: var(--text-inverse);

    &::before {
      content: "✕";
    }
  }

  &.status-warning {
    background: var(--warning-color);
    color: var(--text-inverse);

    &::before {
      content: "!";
    }
  }

  &.status-info {
    background: var(--info-color);
    color: var(--text-inverse);

    &::before {
      content: "i";
    }
  }
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
  border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: var(--radius-sm);
  transition: background var(--transition-base);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-tertiary);
}

::-webkit-scrollbar-corner {
  background: var(--bg-secondary);
}