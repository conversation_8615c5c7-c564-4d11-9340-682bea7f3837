/**
 * 主题管理工具
 * 支持亮色/暗色主题切换，系统主题检测
 */

import { platformApi } from './platform'

class ThemeManager {
  constructor() {
    this.currentTheme = 'auto'
    this.systemTheme = 'light'
    this.listeners = []
    
    this.init()
  }
  
  init() {
    // 检测系统主题偏好
    this.detectSystemTheme()
    
    // 监听系统主题变化
    this.watchSystemTheme()
    
    // 从本地存储恢复主题设置
    this.restoreTheme()
    
    // 应用主题
    this.applyTheme()
  }
  
  /**
   * 检测系统主题偏好
   */
  detectSystemTheme() {
    if (typeof window !== 'undefined' && window.matchMedia) {
      const darkModeQuery = window.matchMedia('(prefers-color-scheme: dark)')
      this.systemTheme = darkModeQuery.matches ? 'dark' : 'light'
    }
  }
  
  /**
   * 监听系统主题变化
   */
  watchSystemTheme() {
    if (typeof window !== 'undefined' && window.matchMedia) {
      const darkModeQuery = window.matchMedia('(prefers-color-scheme: dark)')
      
      darkModeQuery.addEventListener('change', (e) => {
        this.systemTheme = e.matches ? 'dark' : 'light'
        
        // 如果当前是自动模式，重新应用主题
        if (this.currentTheme === 'auto') {
          this.applyTheme()
        }
        
        this.notifyListeners()
      })
    }
  }
  
  /**
   * 从本地存储恢复主题设置
   */
  restoreTheme() {
    try {
      const savedTheme = uni.getStorageSync('theme')
      if (savedTheme && ['light', 'dark', 'black', 'auto'].includes(savedTheme)) {
        this.currentTheme = savedTheme
      }
    } catch (error) {
      console.warn('Failed to restore theme from storage:', error)
    }
  }
  
  /**
   * 保存主题设置到本地存储
   */
  saveTheme() {
    try {
      uni.setStorageSync('theme', this.currentTheme)
    } catch (error) {
      console.warn('Failed to save theme to storage:', error)
    }
  }
  
  /**
   * 设置主题
   * @param {string} theme - 主题名称: 'light', 'dark', 'auto'
   */
  setTheme(theme) {
    if (!['light', 'dark', 'black', 'auto'].includes(theme)) {
      console.warn('Invalid theme:', theme)
      return
    }
    
    this.currentTheme = theme
    this.applyTheme()
    this.saveTheme()
    this.notifyListeners()
  }
  
  /**
   * 应用主题
   */
  applyTheme() {
    const effectiveTheme = this.getEffectiveTheme()
    
    // 设置HTML根元素的data-theme属性
    if (typeof document !== 'undefined') {
      document.documentElement.setAttribute('data-theme', effectiveTheme)
    }
    
    // 设置状态栏样式（uni-app）
    this.setStatusBarStyle(effectiveTheme)
    
    // 设置导航栏样式（uni-app）
    this.setNavigationBarStyle(effectiveTheme)

    // 设置 TabBar 样式（若使用原生TabBar）
    this.setTabBarStyle(effectiveTheme)
  }
  
  /**
   * 获取有效主题（解析auto模式）
   */
  getEffectiveTheme() {
    if (this.currentTheme === 'auto') {
      return this.systemTheme
    }
    return this.currentTheme
  }
  
  /**
   * 设置状态栏样式
   */
  setStatusBarStyle(theme) {
    const isDarkTheme = theme === 'dark' || theme === 'black'
    platformApi.setStatusBarStyle({
      style: isDarkTheme ? 'light' : 'dark'
    })
  }
  
  /**
   * 设置导航栏样式
   */
  setNavigationBarStyle(theme) {
    const isDark = theme === 'dark' || theme === 'black'
    platformApi.setNavigationBarColor({
      frontColor: isDark ? '#ffffff' : '#000000',
      backgroundColor: isDark ? '#1f1f1f' : '#ffffff'
    })
  }

  setTabBarStyle(theme) {
    const isDarkLike = theme === 'dark' || theme === 'black'
    const backgroundColor = theme === 'black' ? '#000000' : (isDarkLike ? '#1a1a1a' : '#FFFFFF')
    const color = isDarkLike ? '#bfbfbf' : '#8B8B8B'
    const borderStyle = isDarkLike ? 'black' : 'white'

    platformApi.setTabBarStyle({
      color,
      selectedColor: '#0052D9',
      backgroundColor,
      borderStyle
    })
  }
  
  /**
   * 切换主题
   */
  toggleTheme() {
    const themes = ['light', 'dark', 'auto']
    const currentIndex = themes.indexOf(this.currentTheme)
    const nextIndex = (currentIndex + 1) % themes.length
    this.setTheme(themes[nextIndex])
  }
  
  /**
   * 获取当前主题
   */
  getCurrentTheme() {
    return this.currentTheme
  }
  
  /**
   * 获取有效主题
   */
  getTheme() {
    return this.getEffectiveTheme()
  }
  
  /**
   * 检查是否为暗色主题
   */
  isDark() {
    const t = this.getEffectiveTheme()
    return t === 'dark' || t === 'black'
  }
  
  /**
   * 检查是否为亮色主题
   */
  isLight() {
    return this.getEffectiveTheme() === 'light'
  }
  
  /**
   * 添加主题变化监听器
   */
  addListener(callback) {
    this.listeners.push(callback)
    
    // 返回移除监听器的函数
    return () => {
      const index = this.listeners.indexOf(callback)
      if (index > -1) {
        this.listeners.splice(index, 1)
      }
    }
  }
  
  /**
   * 通知所有监听器
   */
  notifyListeners() {
    const themeInfo = {
      current: this.currentTheme,
      effective: this.getEffectiveTheme(),
      system: this.systemTheme,
      isDark: this.isDark(),
      isLight: this.isLight()
    }
    
    this.listeners.forEach(callback => {
      try {
        callback(themeInfo)
      } catch (error) {
        console.error('Theme listener error:', error)
      }
    })
  }
  
  /**
   * 获取主题颜色
   */
  getThemeColors() {
    const isDark = this.isDark()
    
    return {
      primary: isDark ? '#4096ff' : '#0052D9',
      secondary: isDark ? '#ffa940' : '#FF8A00',
      background: isDark ? '#141414' : '#ffffff',
      surface: isDark ? '#1f1f1f' : '#fafafa',
      text: isDark ? '#ffffff' : '#262626',
      textSecondary: isDark ? '#d9d9d9' : '#595959',
      border: isDark ? '#434343' : '#d9d9d9',
      success: isDark ? '#73d13d' : '#52c41a',
      warning: isDark ? '#ffc53d' : '#faad14',
      error: isDark ? '#ff7875' : '#ff4d4f',
      info: isDark ? '#40a9ff' : '#1890ff'
    }
  }
  
  /**
   * 预加载主题资源
   */
  preloadThemeAssets() {
    // 预加载暗色主题的关键图片
    if (this.systemTheme === 'dark' || this.currentTheme === 'dark') {
      this.preloadDarkAssets()
    }
    
    // 预加载亮色主题的关键图片
    if (this.systemTheme === 'light' || this.currentTheme === 'light') {
      this.preloadLightAssets()
    }
  }
  
  preloadDarkAssets() {
    // 预加载暗色主题资源
    const darkAssets = [
      '/static/icons/logo-dark.png',
      '/static/backgrounds/hero-dark.jpg'
    ]
    
    darkAssets.forEach(asset => {
      const img = new Image()
      img.src = asset
    })
  }
  
  preloadLightAssets() {
    // 预加载亮色主题资源
    const lightAssets = [
      '/static/icons/logo-light.png',
      '/static/backgrounds/hero-light.jpg'
    ]
    
    lightAssets.forEach(asset => {
      const img = new Image()
      img.src = asset
    })
  }
}

// 创建全局主题管理器实例
export const themeManager = new ThemeManager()

// 导出主题相关的工具函数
export const useTheme = () => {
  return {
    theme: themeManager.getCurrentTheme(),
    effectiveTheme: themeManager.getTheme(),
    isDark: themeManager.isDark(),
    isLight: themeManager.isLight(),
    setTheme: themeManager.setTheme.bind(themeManager),
    toggleTheme: themeManager.toggleTheme.bind(themeManager),
    addListener: themeManager.addListener.bind(themeManager),
    getColors: themeManager.getThemeColors.bind(themeManager)
  }
}

export default themeManager
