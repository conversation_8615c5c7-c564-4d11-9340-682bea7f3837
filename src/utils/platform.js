/**
 * 平台检测工具
 * 用于检测当前运行环境并提供平台特定的功能
 */

/**
 * 获取当前平台信息
 */
export const getPlatform = () => {
  // #ifdef H5
  return 'h5'
  // #endif
  
  // #ifdef MP-WEIXIN
  return 'mp-weixin'
  // #endif
  
  // #ifdef MP-ALIPAY
  return 'mp-alipay'
  // #endif
  
  // #ifdef MP-BAIDU
  return 'mp-baidu'
  // #endif
  
  // #ifdef MP-TOUTIAO
  return 'mp-toutiao'
  // #endif
  
  // #ifdef MP-QQ
  return 'mp-qq'
  // #endif
  
  // #ifdef APP-PLUS
  return 'app-plus'
  // #endif
  
  // #ifdef APP-NVUE
  return 'app-nvue'
  // #endif
  
  return 'unknown'
}

/**
 * 检查是否为H5环境
 */
export const isH5 = () => {
  // #ifdef H5
  return true
  // #endif
  return false
}

/**
 * 检查是否为小程序环境
 */
export const isMiniProgram = () => {
  // #ifdef MP
  return true
  // #endif
  return false
}

/**
 * 检查是否为App环境
 */
export const isApp = () => {
  // #ifdef APP-PLUS
  return true
  // #endif
  return false
}

/**
 * 检查是否为微信小程序
 */
export const isWeixinMiniProgram = () => {
  // #ifdef MP-WEIXIN
  return true
  // #endif
  return false
}

/**
 * 安全调用uni API
 * @param {string} apiName - API名称
 * @param {object} options - API参数
 * @param {function} fallback - 降级处理函数
 */
export const safeUniApi = (apiName, options = {}, fallback = null) => {
  if (typeof uni === 'undefined') {
    console.warn(`uni is not defined, cannot call ${apiName}`)
    return fallback && fallback()
  }
  
  if (typeof uni[apiName] !== 'function') {
    console.warn(`uni.${apiName} is not available on current platform`)
    return fallback && fallback()
  }
  
  try {
    return uni[apiName](options)
  } catch (error) {
    console.warn(`Failed to call uni.${apiName}:`, error.message)
    return fallback && fallback()
  }
}

/**
 * 平台特定的API调用
 */
export const platformApi = {
  /**
   * 设置导航栏颜色
   */
  setNavigationBarColor: (options) => {
    if (isH5()) {
      // H5环境不支持，可以通过CSS来实现
      console.debug('setNavigationBarColor not supported in H5')
      return Promise.resolve()
    }
    
    return safeUniApi('setNavigationBarColor', options, () => {
      console.debug('setNavigationBarColor fallback')
    })
  },

  /**
   * 设置底部 TabBar 样式
   */
  setTabBarStyle: (options) => {
    if (isH5()) {
      // H5环境不支持，交由CSS或自定义Tab处理
      console.debug('setTabBarStyle not supported in H5')
      return Promise.resolve()
    }

    return safeUniApi('setTabBarStyle', options, () => {
      console.debug('setTabBarStyle fallback')
    })
  },
  
  /**
   * 设置状态栏样式
   */
  setStatusBarStyle: (options) => {
    if (isH5()) {
      // H5环境不支持
      console.debug('setStatusBarStyle not supported in H5')
      return Promise.resolve()
    }
    
    return safeUniApi('setStatusBarStyle', options, () => {
      console.debug('setStatusBarStyle fallback')
    })
  },
  
  /**
   * 设置背景颜色
   */
  setBackgroundColor: (options) => {
    if (isH5()) {
      // H5环境可以通过CSS来实现
      if (options.backgroundColor) {
        document.body.style.backgroundColor = options.backgroundColor
      }
      return Promise.resolve()
    }
    
    return safeUniApi('setBackgroundColor', options, () => {
      console.debug('setBackgroundColor fallback')
    })
  },
  
  /**
   * 显示Toast
   */
  showToast: (options) => {
    return safeUniApi('showToast', options, () => {
      // H5降级处理
      if (isH5() && options.title) {
        console.log('Toast:', options.title)
        // 可以在这里实现H5的toast显示逻辑
      }
    })
  },
  
  /**
   * 显示Loading
   */
  showLoading: (options) => {
    return safeUniApi('showLoading', options, () => {
      if (isH5() && options.title) {
        console.log('Loading:', options.title)
      }
    })
  },
  
  /**
   * 隐藏Loading
   */
  hideLoading: () => {
    return safeUniApi('hideLoading', {}, () => {
      if (isH5()) {
        console.log('Hide Loading')
      }
    })
  },
  
  /**
   * 获取系统信息
   */
  getSystemInfo: () => {
    return new Promise((resolve, reject) => {
      if (typeof uni === 'undefined') {
        // 降级处理
        resolve({
          platform: 'h5',
          system: navigator.userAgent,
          statusBarHeight: 0,
          windowWidth: window.innerWidth,
          windowHeight: window.innerHeight
        })
        return
      }
      
      try {
        uni.getSystemInfo({
          success: resolve,
          fail: reject
        })
      } catch (error) {
        reject(error)
      }
    })
  },
  
  /**
   * 存储数据
   */
  setStorage: (key, data) => {
    if (isH5()) {
      try {
        localStorage.setItem(key, JSON.stringify(data))
        return Promise.resolve()
      } catch (error) {
        return Promise.reject(error)
      }
    }
    
    return new Promise((resolve, reject) => {
      safeUniApi('setStorage', {
        key,
        data,
        success: resolve,
        fail: reject
      }, () => reject(new Error('setStorage not available')))
    })
  },
  
  /**
   * 获取存储数据
   */
  getStorage: (key) => {
    if (isH5()) {
      try {
        const data = localStorage.getItem(key)
        return Promise.resolve({ data: data ? JSON.parse(data) : null })
      } catch (error) {
        return Promise.reject(error)
      }
    }
    
    return new Promise((resolve, reject) => {
      safeUniApi('getStorage', {
        key,
        success: resolve,
        fail: reject
      }, () => reject(new Error('getStorage not available')))
    })
  }
}

/**
 * 获取平台特定的样式类名
 */
export const getPlatformClass = () => {
  const platform = getPlatform()
  return `platform-${platform}`
}

/**
 * 检查API是否可用
 */
export const isApiAvailable = (apiName) => {
  return typeof uni !== 'undefined' && typeof uni[apiName] === 'function'
}

/**
 * 平台特定的配置
 */
export const platformConfig = {
  h5: {
    supportNavigationBarColor: false,
    supportStatusBarStyle: false,
    supportBackgroundColor: true, // 通过CSS实现
    supportVibrate: false
  },
  'mp-weixin': {
    supportNavigationBarColor: true,
    supportStatusBarStyle: true,
    supportBackgroundColor: true,
    supportVibrate: true
  },
  'app-plus': {
    supportNavigationBarColor: true,
    supportStatusBarStyle: true,
    supportBackgroundColor: true,
    supportVibrate: true
  }
}

/**
 * 获取当前平台的配置
 */
export const getCurrentPlatformConfig = () => {
  const platform = getPlatform()
  return platformConfig[platform] || platformConfig.h5
}

export default {
  getPlatform,
  isH5,
  isMiniProgram,
  isApp,
  isWeixinMiniProgram,
  safeUniApi,
  platformApi,
  getPlatformClass,
  isApiAvailable,
  getCurrentPlatformConfig
}
